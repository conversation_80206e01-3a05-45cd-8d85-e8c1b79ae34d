package auth

import (
	"net/http"

	"omnicore/backend/internal/services"

	"github.com/gin-gonic/gin"
)

// <PERSON><PERSON> handles authentication HTTP requests
type Handler struct {
	authService services.AuthServiceInterface
}

// NewHandler creates a new authentication handler
func NewHandler(authService services.AuthServiceInterface) *Handler {
	return &Handler{
		authService: authService,
	}
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
}

// Register handles user registration
// @Summary Register a new user
// @Description Create a new user account with email and password
// @Tags auth
// @Accept json
// @Produce json
// @Param request body services.RegisterRequest true "Registration request"
// @Success 201 {object} SuccessResponse{data=services.AuthResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/register [post]
func (h *Handler) Register(c *gin.Context) {
	var req services.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "validation_error",
			Message: err.Error(),
		})
		return
	}

	response, err := h.authService.Register(&req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorType := "internal_error"

		// Handle specific error types
		switch err.Error() {
		case "email already exists for this tenant":
			statusCode = http.StatusConflict
			errorType = "email_exists"
		case "tenant not found":
			statusCode = http.StatusBadRequest
			errorType = "tenant_not_found"
		case "invalid email format", "invalid role":
			statusCode = http.StatusBadRequest
			errorType = "validation_error"
		}

		if statusCode == http.StatusBadRequest || statusCode == http.StatusConflict {
			errorType = "validation_error"
		}

		c.JSON(statusCode, ErrorResponse{
			Error:   errorType,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Success: true,
		Data:    response,
		Message: "User registered successfully",
	})
}

// Login handles user authentication
// @Summary Login user
// @Description Authenticate user with email and password
// @Tags auth
// @Accept json
// @Produce json
// @Param request body services.LoginRequest true "Login request"
// @Success 200 {object} SuccessResponse{data=services.AuthResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req services.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "validation_error",
			Message: err.Error(),
		})
		return
	}

	response, err := h.authService.Login(&req)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorType := "internal_error"

		if err.Error() == "invalid credentials" {
			statusCode = http.StatusUnauthorized
			errorType = "invalid_credentials"
		}

		c.JSON(statusCode, ErrorResponse{
			Error:   errorType,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Data:    response,
		Message: "Login successful",
	})
}

// Me returns current user information
// @Summary Get current user
// @Description Get current authenticated user information
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} SuccessResponse{data=models.User}
// @Failure 401 {object} ErrorResponse
// @Router /api/v1/auth/me [get]
func (h *Handler) Me(c *gin.Context) {
	// Get user from context (set by auth middleware)
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "User not found in context",
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Data:    user,
		Message: "User information retrieved successfully",
	})
}

// RegisterRoutes registers authentication routes
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	auth := router.Group("/auth")
	{
		auth.POST("/register", h.Register)
		auth.POST("/login", h.Login)
		auth.GET("/me", h.Me) // This will require auth middleware
	}
}
