(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{2905:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(4568),a=s(8500),n=s(7620),l=s(2942);function i(e){let{children:t,requiredRole:s,fallbackPath:i="/login"}=e,o=(0,l.useRouter)(),{user:c,isAuthenticated:d,isLoading:u}=(0,a.As)();return((0,n.useEffect)(()=>{if(!u){if(!d)return void o.push(i);if(s&&(null==c?void 0:c.role)!==s)return void o.push("/unauthorized")}},[d,u,c,s,o,i]),u)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"})}):!d||s&&(null==c?void 0:c.role)!==s?null:(0,r.jsx)(r.Fragment,{children:t})}function o(){let{user:e}=(0,a.As)(),{logout:t}=(0,a.BG)();return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("nav",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"OmniCore Dashboard"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",null==e?void 0:e.email]}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800",children:null==e?void 0:e.role}),(0,r.jsx)("button",{onClick:()=>{t()},className:"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Logout"})]})]})})}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"px-4 py-6 sm:px-0",children:(0,r.jsx)("div",{className:"border-4 border-dashed border-gray-200 rounded-lg p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Welcome to OmniCore!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"You have successfully logged in to the system."}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 max-w-md mx-auto",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Your Profile"}),(0,r.jsxs)("dl",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email:"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:null==e?void 0:e.email})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Role:"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:null==e?void 0:e.role})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Tenant ID:"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:null==e?void 0:e.tenantId})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"User ID:"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900 font-mono text-xs",children:null==e?void 0:e.id})]})]})]}),(null==e?void 0:e.role)==="Administrator"&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-blue-800 font-medium",children:"\uD83D\uDD11 Administrator Access"}),(0,r.jsx)("p",{className:"text-blue-600 text-sm mt-1",children:"You have full access to all system features including user management."})]}),(null==e?void 0:e.role)==="Accountant"&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-green-800 font-medium",children:"\uD83D\uDCCA Accountant Access"}),(0,r.jsx)("p",{className:"text-green-600 text-sm mt-1",children:"You have access to financial features and reporting."})]}),(null==e?void 0:e.role)==="POS Staff"&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-purple-50 rounded-lg",children:[(0,r.jsx)("p",{className:"text-purple-800 font-medium",children:"\uD83D\uDED2 POS Staff Access"}),(0,r.jsx)("p",{className:"text-purple-600 text-sm mt-1",children:"You have access to point-of-sale features and inventory."})]})]})})})})]})}function c(){return(0,r.jsx)(i,{children:(0,r.jsx)(o,{})})}},2942:(e,t,s)=>{"use strict";var r=s(2418);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},8254:(e,t,s)=>{Promise.resolve().then(s.bind(s,2905))},8500:(e,t,s)=>{"use strict";s.d(t,{As:()=>c,BG:()=>d,nc:()=>o});var r=s(1326),a=s(5313),n=s(7932);let l=s(4338).env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api/v1",i={async post(e,t,s){let r={"Content-Type":"application/json"};s&&(r.Authorization="Bearer ".concat(s));let a=await fetch("".concat(l).concat(e),{method:"POST",headers:r,body:JSON.stringify(t)}),n=await a.json();if(!a.ok)throw Error(n.message||n.error||"An error occurred");return n},async get(e,t){let s={};t&&(s.Authorization="Bearer ".concat(t));let r=await fetch("".concat(l).concat(e),{method:"GET",headers:s}),a=await r.json();if(!r.ok)throw Error(a.message||a.error||"An error occurred");return a}},o=(0,r.v)()((0,a.Zr)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{try{e({isLoading:!0,error:null});let{token:s,user:r}=(await i.post("/auth/login",t)).data;n.A.set("auth_token",s,{expires:1,secure:!0,sameSite:"strict"}),e({user:r,token:s,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Login failed"}),t}},register:async t=>{try{e({isLoading:!0,error:null});let{token:s,user:r}=(await i.post("/auth/register",t)).data;n.A.set("auth_token",s,{expires:1,secure:!0,sameSite:"strict"}),e({user:r,token:s,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Registration failed"}),t}},logout:()=>{n.A.remove("auth_token"),e({user:null,token:null,isAuthenticated:!1,error:null})},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},refreshUser:async()=>{try{let{token:s}=t();if(!s)return;let r=await i.get("/auth/me",s);e({user:r.data,isAuthenticated:!0})}catch(e){t().logout()}}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated}),onRehydrateStorage:()=>e=>{let t=n.A.get("auth_token");t&&e?(e.token=t,e.refreshUser()):e&&e.logout()}})),c=()=>{let{user:e,isAuthenticated:t,isLoading:s}=o();return{user:e,isAuthenticated:t,isLoading:s}},d=()=>{let{login:e,register:t,logout:s,clearError:r}=o();return{login:e,register:t,logout:s,clearError:r}}}},e=>{e.O(0,[767,587,902,358],()=>e(e.s=8254)),_N_E=e.O()}]);