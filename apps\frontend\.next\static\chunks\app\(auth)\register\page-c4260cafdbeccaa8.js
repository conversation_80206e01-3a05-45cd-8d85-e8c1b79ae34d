(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[678],{2942:(e,r,t)=>{"use strict";var s=t(2418);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},7746:(e,r,t)=>{Promise.resolve().then(t.bind(t,9674))},8500:(e,r,t)=>{"use strict";t.d(r,{As:()=>l,BG:()=>c,nc:()=>d});var s=t(1326),a=t(5313),o=t(7932);let n=t(4338).env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api/v1",i={async post(e,r,t){let s={"Content-Type":"application/json"};t&&(s.Authorization="Bearer ".concat(t));let a=await fetch("".concat(n).concat(e),{method:"POST",headers:s,body:JSON.stringify(r)}),o=await a.json();if(!a.ok)throw Error(o.message||o.error||"An error occurred");return o},async get(e,r){let t={};r&&(t.Authorization="Bearer ".concat(r));let s=await fetch("".concat(n).concat(e),{method:"GET",headers:t}),a=await s.json();if(!s.ok)throw Error(a.message||a.error||"An error occurred");return a}},d=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async r=>{try{e({isLoading:!0,error:null});let{token:t,user:s}=(await i.post("/auth/login",r)).data;o.A.set("auth_token",t,{expires:1,secure:!0,sameSite:"strict"}),e({user:s,token:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){throw e({isLoading:!1,error:r instanceof Error?r.message:"Login failed"}),r}},register:async r=>{try{e({isLoading:!0,error:null});let{token:t,user:s}=(await i.post("/auth/register",r)).data;o.A.set("auth_token",t,{expires:1,secure:!0,sameSite:"strict"}),e({user:s,token:t,isAuthenticated:!0,isLoading:!1,error:null})}catch(r){throw e({isLoading:!1,error:r instanceof Error?r.message:"Registration failed"}),r}},logout:()=>{o.A.remove("auth_token"),e({user:null,token:null,isAuthenticated:!1,error:null})},clearError:()=>{e({error:null})},setLoading:r=>{e({isLoading:r})},refreshUser:async()=>{try{let{token:t}=r();if(!t)return;let s=await i.get("/auth/me",t);e({user:s.data,isAuthenticated:!0})}catch(e){r().logout()}}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated}),onRehydrateStorage:()=>e=>{let r=o.A.get("auth_token");r&&e?(e.token=r,e.refreshUser()):e&&e.logout()}})),l=()=>{let{user:e,isAuthenticated:r,isLoading:t}=d();return{user:e,isAuthenticated:r,isLoading:t}},c=()=>{let{login:e,register:r,logout:t,clearError:s}=d();return{login:e,register:r,logout:t,clearError:s}}},9674:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(4568),a=t(7620),o=t(2942),n=t(7261),i=t.n(n),d=t(8500);function l(){let e=(0,o.useRouter)(),{register:r,isLoading:t,error:n,clearError:l,isAuthenticated:c}=(0,d.nc)(),[u,m]=(0,a.useState)({tenant_id:"",email:"",password:"",role:"POS Staff"}),[h,p]=(0,a.useState)(""),[x,g]=(0,a.useState)({});(0,a.useEffect)(()=>{c&&e.push("/dashboard")},[c,e]),(0,a.useEffect)(()=>{l()},[l]);let f=async t=>{if(t.preventDefault(),(()=>{let e={};if(u.tenant_id.trim()||(e.tenant_id="Tenant ID is required"),u.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u.email)||(e.email="Please enter a valid email address"):e.email="Email is required",u.password)if(u.password.length<8)e.password="Password must be at least 8 characters long";else{let r=/[A-Z]/.test(u.password),t=/[a-z]/.test(u.password),s=/[0-9]/.test(u.password);r?t?s||(e.password="Password must contain at least one digit"):e.password="Password must contain at least one lowercase letter":e.password="Password must contain at least one uppercase letter"}else e.password="Password is required";return h?u.password!==h&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",u.role||(e.role="Please select a role"),g(e),0===Object.keys(e).length})())try{await r(u),e.push("/dashboard")}catch(e){console.error("Registration failed:",e)}},w=e=>{let{name:r,value:t}=e.target;"confirmPassword"===r?p(t):m(e=>({...e,[r]:t})),x[r]&&g(e=>({...e,[r]:void 0}))};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your OmniCore account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(i(),{href:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"sign in to your existing account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[n&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"text-sm text-red-700",children:n})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"tenant_id",className:"block text-sm font-medium text-gray-700",children:"Tenant ID"}),(0,s.jsx)("input",{id:"tenant_id",name:"tenant_id",type:"text",required:!0,value:u.tenant_id,onChange:w,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(x.tenant_id?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Enter your tenant ID"}),x.tenant_id&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.tenant_id})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:u.email,onChange:w,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(x.email?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Enter your email"}),x.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,s.jsxs)("select",{id:"role",name:"role",required:!0,value:u.role,onChange:w,className:"mt-1 block w-full px-3 py-2 border ".concat(x.role?"border-red-300":"border-gray-300"," bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),children:[(0,s.jsx)("option",{value:"POS Staff",children:"POS Staff"}),(0,s.jsx)("option",{value:"Accountant",children:"Accountant"}),(0,s.jsx)("option",{value:"Administrator",children:"Administrator"})]}),x.role&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.role})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:u.password,onChange:w,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(x.password?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Password (8+ chars, uppercase, lowercase, digit)"}),x.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.password})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:h,onChange:w,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(x.confirmPassword?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Confirm your password"}),x.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:x.confirmPassword})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:t?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})})]})]})})}}},e=>{e.O(0,[767,261,587,902,358],()=>e(e.s=7746)),_N_E=e.O()}]);