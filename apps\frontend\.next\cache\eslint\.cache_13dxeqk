[{"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\stores\\auth-store.ts": "3", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\login\\page.tsx": "4", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\register\\page.tsx": "5", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\unauthorized\\page.tsx": "7", "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "8"}, {"size": 689, "mtime": 1754313585056, "results": "9", "hashOfConfig": "10"}, {"size": 790, "mtime": 1754320385714, "results": "11", "hashOfConfig": "10"}, {"size": 5574, "mtime": 1754320659068, "results": "12", "hashOfConfig": "10"}, {"size": 6701, "mtime": 1754320580905, "results": "13", "hashOfConfig": "10"}, {"size": 9459, "mtime": 1754320553428, "results": "14", "hashOfConfig": "10"}, {"size": 4634, "mtime": 1754320320399, "results": "15", "hashOfConfig": "10"}, {"size": 2962, "mtime": 1754320705790, "results": "16", "hashOfConfig": "10"}, {"size": 2188, "mtime": 1754320299177, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1okb4zw", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\stores\\auth-store.ts", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], []]