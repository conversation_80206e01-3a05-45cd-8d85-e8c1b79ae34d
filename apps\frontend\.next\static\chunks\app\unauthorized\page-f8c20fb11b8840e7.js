(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{2617:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(4568),a=r(7261),n=r.n(a),o=r(8500);function i(){let{user:e}=(0,o.As)();return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 text-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 text-red-600",children:(0,s.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Access Denied"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"You don't have permission to access this resource."})]}),(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Current Access Level"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Email:"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:null==e?void 0:e.email})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Role:"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:null==e?void 0:e.role})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"If you believe you should have access to this resource, please contact your administrator."}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)(n(),{href:"/dashboard",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Go to Dashboard"}),(0,s.jsx)(n(),{href:"/login",className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Sign in with different account"})]})]})]})})}},7806:(e,t,r)=>{Promise.resolve().then(r.bind(r,2617))},8500:(e,t,r)=>{"use strict";r.d(t,{As:()=>l,BG:()=>d,nc:()=>c});var s=r(1326),a=r(5313),n=r(7932);let o=r(4338).env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api/v1",i={async post(e,t,r){let s={"Content-Type":"application/json"};r&&(s.Authorization="Bearer ".concat(r));let a=await fetch("".concat(o).concat(e),{method:"POST",headers:s,body:JSON.stringify(t)}),n=await a.json();if(!a.ok)throw Error(n.message||n.error||"An error occurred");return n},async get(e,t){let r={};t&&(r.Authorization="Bearer ".concat(t));let s=await fetch("".concat(o).concat(e),{method:"GET",headers:r}),a=await s.json();if(!s.ok)throw Error(a.message||a.error||"An error occurred");return a}},c=(0,s.v)()((0,a.Zr)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{try{e({isLoading:!0,error:null});let{token:r,user:s}=(await i.post("/auth/login",t)).data;n.A.set("auth_token",r,{expires:1,secure:!0,sameSite:"strict"}),e({user:s,token:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Login failed"}),t}},register:async t=>{try{e({isLoading:!0,error:null});let{token:r,user:s}=(await i.post("/auth/register",t)).data;n.A.set("auth_token",r,{expires:1,secure:!0,sameSite:"strict"}),e({user:s,token:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Registration failed"}),t}},logout:()=>{n.A.remove("auth_token"),e({user:null,token:null,isAuthenticated:!1,error:null})},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},refreshUser:async()=>{try{let{token:r}=t();if(!r)return;let s=await i.get("/auth/me",r);e({user:s.data,isAuthenticated:!0})}catch(e){t().logout()}}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated}),onRehydrateStorage:()=>e=>{let t=n.A.get("auth_token");t&&e?(e.token=t,e.refreshUser()):e&&e.logout()}})),l=()=>{let{user:e,isAuthenticated:t,isLoading:r}=c();return{user:e,isAuthenticated:t,isLoading:r}},d=()=>{let{login:e,register:t,logout:r,clearError:s}=c();return{login:e,register:t,logout:r,clearError:s}}}},e=>{e.O(0,[767,261,587,902,358],()=>e(e.s=7806)),_N_E=e.O()}]);