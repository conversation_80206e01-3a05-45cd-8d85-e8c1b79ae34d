(()=>{var a={};a.id=678,a.ids=[678],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},685:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(8828),e=c(8935),f=c.n(e),g=c(1579),h=c.n(g);c(4276);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},2974:(a,b,c)=>{Promise.resolve().then(c.bind(c,3682))},2984:(a,b,c)=>{"use strict";var d=c(860);c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3682:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(6352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Project\\\\OmniCore\\\\apps\\\\frontend\\\\src\\\\app\\\\(auth)\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\register\\page.tsx","default")},3873:a=>{"use strict";a.exports=require("path")},4108:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(3486),e=c(159),f=c(2984),g=c(9989),h=c.n(g),i=c(8310);function j(){let a=(0,f.useRouter)(),{register:b,isLoading:c,error:g,clearError:j,isAuthenticated:k}=(0,i.nc)(),[l,m]=(0,e.useState)({tenant_id:"",email:"",password:"",role:"POS Staff"}),[n,o]=(0,e.useState)(""),[p,q]=(0,e.useState)({}),r=async c=>{if(c.preventDefault(),(()=>{let a={};return l.tenant_id.trim()||(a.tenant_id="Tenant ID is required"),l.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(l.email)||(a.email="Please enter a valid email address"):a.email="Email is required",l.password?l.password.length<8&&(a.password="Password must be at least 8 characters long"):a.password="Password is required",n?l.password!==n&&(a.confirmPassword="Passwords do not match"):a.confirmPassword="Please confirm your password",l.role||(a.role="Please select a role"),q(a),0===Object.keys(a).length})())try{await b(l),a.push("/dashboard")}catch(a){console.error("Registration failed:",a)}},s=a=>{let{name:b,value:c}=a.target;"confirmPassword"===b?o(c):m(a=>({...a,[b]:c})),p[b]&&q(a=>({...a,[b]:void 0}))};return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your OmniCore account"}),(0,d.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,d.jsx)(h(),{href:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"sign in to your existing account"})]})]}),(0,d.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:r,children:[g&&(0,d.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,d.jsx)("div",{className:"text-sm text-red-700",children:g})}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"tenant_id",className:"block text-sm font-medium text-gray-700",children:"Tenant ID"}),(0,d.jsx)("input",{id:"tenant_id",name:"tenant_id",type:"text",required:!0,value:l.tenant_id,onChange:s,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${p.tenant_id?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`,placeholder:"Enter your tenant ID"}),p.tenant_id&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.tenant_id})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,d.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:l.email,onChange:s,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${p.email?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`,placeholder:"Enter your email"}),p.email&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,d.jsxs)("select",{id:"role",name:"role",required:!0,value:l.role,onChange:s,className:`mt-1 block w-full px-3 py-2 border ${p.role?"border-red-300":"border-gray-300"} bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,children:[(0,d.jsx)("option",{value:"POS Staff",children:"POS Staff"}),(0,d.jsx)("option",{value:"Accountant",children:"Accountant"}),(0,d.jsx)("option",{value:"Administrator",children:"Administrator"})]}),p.role&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.role})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,d.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:l.password,onChange:s,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${p.password?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`,placeholder:"Enter your password (min 8 characters)"}),p.password&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.password})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,d.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:n,onChange:s,className:`mt-1 appearance-none relative block w-full px-3 py-2 border ${p.confirmPassword?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm`,placeholder:"Confirm your password"}),p.confirmPassword&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.confirmPassword})]})]}),(0,d.jsx)("div",{children:(0,d.jsx)("button",{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})})]})]})})}},4276:()=>{},4763:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3824,23)),Promise.resolve().then(c.t.bind(c,9355,23)),Promise.resolve().then(c.t.bind(c,4439,23)),Promise.resolve().then(c.t.bind(c,4730,23)),Promise.resolve().then(c.t.bind(c,9774,23)),Promise.resolve().then(c.t.bind(c,3170,23)),Promise.resolve().then(c.t.bind(c,968,23)),Promise.resolve().then(c.t.bind(c,8298,23)),Promise.resolve().then(c.t.bind(c,282,23))},5379:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,2526,23)),Promise.resolve().then(c.t.bind(c,385,23)),Promise.resolve().then(c.t.bind(c,3737,23)),Promise.resolve().then(c.t.bind(c,1904,23)),Promise.resolve().then(c.t.bind(c,5856,23)),Promise.resolve().then(c.t.bind(c,5492,23)),Promise.resolve().then(c.t.bind(c,9082,23)),Promise.resolve().then(c.t.bind(c,5812,23)),Promise.resolve().then(c.bind(c,3220))},5813:()=>{},6022:(a,b,c)=>{Promise.resolve().then(c.bind(c,4108))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8310:(a,b,c)=>{"use strict";c.d(b,{As:()=>j,BG:()=>k,nc:()=>i});var d=c(8208),e=c(1009),f=c(532);let g=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api/v1",h={async post(a,b,c){let d={"Content-Type":"application/json"};c&&(d.Authorization=`Bearer ${c}`);let e=await fetch(`${g}${a}`,{method:"POST",headers:d,body:JSON.stringify(b)}),f=await e.json();if(!e.ok)throw Error(f.message||f.error||"An error occurred");return f},async get(a,b){let c={};b&&(c.Authorization=`Bearer ${b}`);let d=await fetch(`${g}${a}`,{method:"GET",headers:c}),e=await d.json();if(!d.ok)throw Error(e.message||e.error||"An error occurred");return e}},i=(0,d.v)()((0,e.Zr)((a,b)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async b=>{try{a({isLoading:!0,error:null});let{token:c,user:d}=(await h.post("/auth/login",b)).data;f.A.set("auth_token",c,{expires:1,secure:!0,sameSite:"strict"}),a({user:d,token:c,isAuthenticated:!0,isLoading:!1,error:null})}catch(b){throw a({isLoading:!1,error:b instanceof Error?b.message:"Login failed"}),b}},register:async b=>{try{a({isLoading:!0,error:null});let{token:c,user:d}=(await h.post("/auth/register",b)).data;f.A.set("auth_token",c,{expires:1,secure:!0,sameSite:"strict"}),a({user:d,token:c,isAuthenticated:!0,isLoading:!1,error:null})}catch(b){throw a({isLoading:!1,error:b instanceof Error?b.message:"Registration failed"}),b}},logout:()=>{f.A.remove("auth_token"),a({user:null,token:null,isAuthenticated:!1,error:null})},clearError:()=>{a({error:null})},setLoading:b=>{a({isLoading:b})},refreshUser:async()=>{try{let{token:c}=b();if(!c)return;let d=await h.get("/auth/me",c);a({user:d.data,isAuthenticated:!0})}catch{b().logout()}}}),{name:"auth-storage",partialize:a=>({user:a.user,token:a.token,isAuthenticated:a.isAuthenticated}),onRehydrateStorage:()=>a=>{let b=f.A.get("auth_token");b&&a?(a.token=b,a.refreshUser()):a&&a.logout()}})),j=()=>{let{user:a,isAuthenticated:b,isLoading:c}=i();return{user:a,isAuthenticated:b,isLoading:c}},k=()=>{let{login:a,register:b,logout:c,clearError:d}=i();return{login:a,register:b,logout:c,clearError:d}}},8354:a=>{"use strict";a.exports=require("util")},8965:()=>{},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(4332),e=c(8819),f=c(3949),g=c(8730),h=c(8996),i=c(6318),j=c(3093),k=c(6748),l=c(8190),m=c(3904),n=c(7735),o=c(611),p=c(2512),q=c(261),r=c(3863),s=c(8748),t=c(6713),u=c(5262),v=c(7779),w=c(5303),x=c(6704),y=c(7656),z=c(3072),A=c(6439),B=c(3824),C=c.n(B),D=c(7540),E=c(9005),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,3682)),"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\register\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(c.t.bind(c,134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,5983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,4482,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,685)),"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,3824,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,5983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,4482,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,9699))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\register\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(auth)/register/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},9699:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1253);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[699,61,597,989],()=>b(b.s=9431));module.exports=c})();