'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/stores/auth-store';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: 'Administrator' | 'Accountant' | 'POS Staff';
  fallbackPath?: string;
}

export default function ProtectedRoute({ 
  children, 
  requiredRole,
  fallbackPath = '/login' 
}: ProtectedRouteProps) {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return;

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      router.push(fallbackPath);
      return;
    }

    // Check role-based access if required
    if (requiredRole && user?.role !== requiredRole) {
      // For role-based restrictions, redirect to unauthorized page or dashboard
      router.push('/unauthorized');
      return;
    }
  }, [isAuthenticated, isLoading, user, requiredRole, router, fallbackPath]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  // Don't render children if not authenticated or wrong role
  if (!isAuthenticated || (requiredRole && user?.role !== requiredRole)) {
    return null;
  }

  return <>{children}</>;
}

// Higher-order component for admin-only routes
export function AdminRoute({ children }: { children: ReactNode }) {
  return (
    <ProtectedRoute requiredRole="Administrator" fallbackPath="/unauthorized">
      {children}
    </ProtectedRoute>
  );
}

// Higher-order component for accountant or admin routes
export function AccountantRoute({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  
  if (user?.role === 'Administrator' || user?.role === 'Accountant') {
    return <ProtectedRoute>{children}</ProtectedRoute>;
  }

  return (
    <ProtectedRoute requiredRole="Accountant" fallbackPath="/unauthorized">
      {children}
    </ProtectedRoute>
  );
}
