globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/unauthorized/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"700":{"*":{"id":"9082","name":"*","chunks":[],"async":false}},"1256":{"*":{"id":"2526","name":"*","chunks":[],"async":false}},"2617":{"*":{"id":"4401","name":"*","chunks":[],"async":false}},"2905":{"*":{"id":"3882","name":"*","chunks":[],"async":false}},"3283":{"*":{"id":"3737","name":"*","chunks":[],"async":false}},"4712":{"*":{"id":"1904","name":"*","chunks":[],"async":false}},"4780":{"*":{"id":"3220","name":"*","chunks":[],"async":false}},"5082":{"*":{"id":"5812","name":"*","chunks":[],"async":false}},"5604":{"*":{"id":"7272","name":"*","chunks":[],"async":false}},"7132":{"*":{"id":"5856","name":"*","chunks":[],"async":false}},"7738":{"*":{"id":"6628","name":"*","chunks":[],"async":false}},"7748":{"*":{"id":"5492","name":"*","chunks":[],"async":false}},"9065":{"*":{"id":"385","name":"*","chunks":[],"async":false}},"9674":{"*":{"id":"4108","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":1256,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":1256,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":9065,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":9065,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":3283,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":3283,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":4712,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":4712,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7132,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7132,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":7748,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":7748,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":700,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":700,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":5082,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":5082,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":4780,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":4780,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":4703,"name":"*","chunks":["177","static/chunks/app/layout-299920cea797dbe2.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7573,"name":"*","chunks":["177","static/chunks/app/layout-299920cea797dbe2.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\globals.css":{"id":3139,"name":"*","chunks":["177","static/chunks/app/layout-299920cea797dbe2.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\login\\page.tsx":{"id":5604,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\(auth)\\register\\page.tsx":{"id":9674,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\dashboard\\page.tsx":{"id":2905,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page.tsx":{"id":7738,"name":"*","chunks":["767","static/chunks/767-fbaae1a75d8191e5.js","974","static/chunks/app/page-30eb896c15554512.js"],"async":false},"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\unauthorized\\page.tsx":{"id":2617,"name":"*","chunks":["767","static/chunks/767-fbaae1a75d8191e5.js","261","static/chunks/261-a65c5532d55fb83f.js","305","static/chunks/app/unauthorized/page-f8c20fb11b8840e7.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\":[],"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/2e851f5f0a41dd30.css"}],"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\Project\\OmniCore\\apps\\frontend\\src\\app\\unauthorized\\page":[]},"rscModuleMapping":{"700":{"*":{"id":"968","name":"*","chunks":[],"async":false}},"1256":{"*":{"id":"3824","name":"*","chunks":[],"async":false}},"2617":{"*":{"id":"23","name":"*","chunks":[],"async":false}},"2905":{"*":{"id":"3049","name":"*","chunks":[],"async":false}},"3139":{"*":{"id":"4276","name":"*","chunks":[],"async":false}},"3283":{"*":{"id":"4439","name":"*","chunks":[],"async":false}},"4712":{"*":{"id":"4730","name":"*","chunks":[],"async":false}},"4780":{"*":{"id":"282","name":"*","chunks":[],"async":false}},"5082":{"*":{"id":"8298","name":"*","chunks":[],"async":false}},"5604":{"*":{"id":"7561","name":"*","chunks":[],"async":false}},"7132":{"*":{"id":"9774","name":"*","chunks":[],"async":false}},"7738":{"*":{"id":"7858","name":"*","chunks":[],"async":false}},"7748":{"*":{"id":"3170","name":"*","chunks":[],"async":false}},"9065":{"*":{"id":"9355","name":"*","chunks":[],"async":false}},"9674":{"*":{"id":"3682","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{"700":{"*":{"id":"9082","name":"*","chunks":[],"async":false}},"1256":{"*":{"id":"2526","name":"*","chunks":[],"async":false}},"3283":{"*":{"id":"3737","name":"*","chunks":[],"async":false}},"4712":{"*":{"id":"1904","name":"*","chunks":[],"async":false}},"4780":{"*":{"id":"3220","name":"*","chunks":[],"async":false}},"5082":{"*":{"id":"5812","name":"*","chunks":[],"async":false}},"7132":{"*":{"id":"5856","name":"*","chunks":[],"async":false}},"7748":{"*":{"id":"5492","name":"*","chunks":[],"async":false}},"9065":{"*":{"id":"385","name":"*","chunks":[],"async":false}}}}