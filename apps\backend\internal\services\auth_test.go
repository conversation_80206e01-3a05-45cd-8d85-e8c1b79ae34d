package services

import (
	"os"
	"testing"

	"omnicore/backend/internal/models"
	"omnicore/backend/internal/repositories"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockUserRepository is a mock implementation of UserRepository
type MockUserRepository struct {
	mock.Mock
}

// Ensure MockUserRepository implements UserRepositoryInterface
var _ repositories.UserRepositoryInterface = (*MockUserRepository)(nil)

func (m *MockUserRepository) CreateUser(user *models.User) error {
	args := m.Called(user)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserByEmail(tenantID, email string) (*models.User, error) {
	args := m.Called(tenantID, email)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByID(tenantID, userID string) (*models.User, error) {
	args := m.Called(tenantID, userID)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) UpdateUserRole(tenantID, userID, role string) error {
	args := m.Called(tenantID, userID, role)
	return args.Error(0)
}

func (m *MockUserRepository) ListUsers(tenantID string, limit, offset int) ([]models.User, int64, error) {
	args := m.Called(tenantID, limit, offset)
	return args.Get(0).([]models.User), args.Get(1).(int64), args.Error(2)
}

// MockTenantRepository is a mock implementation of TenantRepository
type MockTenantRepository struct {
	mock.Mock
}

// Ensure MockTenantRepository implements TenantRepositoryInterface
var _ repositories.TenantRepositoryInterface = (*MockTenantRepository)(nil)

func (m *MockTenantRepository) CreateTenant(tenant *models.Tenant) error {
	args := m.Called(tenant)
	return args.Error(0)
}

func (m *MockTenantRepository) GetTenantByID(tenantID string) (*models.Tenant, error) {
	args := m.Called(tenantID)
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func TestAuthService_Register(t *testing.T) {
	// Set up test environment
	os.Setenv("JWT_SECRET", "test-secret")
	defer os.Unsetenv("JWT_SECRET")

	mockUserRepo := new(MockUserRepository)
	mockTenantRepo := new(MockTenantRepository)
	authService := NewAuthService(mockUserRepo, mockTenantRepo)

	tests := []struct {
		name        string
		request     *RegisterRequest
		setupMocks  func()
		expectError bool
		errorMsg    string
	}{
		{
			name: "successful registration",
			request: &RegisterRequest{
				TenantID: "tenant-123",
				Email:    "<EMAIL>",
				Password: "Password123",
				Role:     models.RoleAdministrator,
			},
			setupMocks: func() {
				mockUserRepo.On("CreateUser", mock.AnythingOfType("*models.User")).Return(nil)
			},
			expectError: false,
		},
		{
			name: "invalid email format",
			request: &RegisterRequest{
				TenantID: "tenant-123",
				Email:    "invalid-email",
				Password: "password123",
				Role:     models.RoleAdministrator,
			},
			setupMocks:  func() {},
			expectError: true,
			errorMsg:    "invalid email format",
		},
		{
			name: "password too short",
			request: &RegisterRequest{
				TenantID: "tenant-123",
				Email:    "<EMAIL>",
				Password: "short",
				Role:     models.RoleAdministrator,
			},
			setupMocks:  func() {},
			expectError: true,
			errorMsg:    "password must be at least 8 characters long",
		},
		{
			name: "invalid role",
			request: &RegisterRequest{
				TenantID: "tenant-123",
				Email:    "<EMAIL>",
				Password: "Password123",
				Role:     "InvalidRole",
			},
			setupMocks:  func() {},
			expectError: true,
			errorMsg:    "invalid role",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockUserRepo.ExpectedCalls = nil
			mockTenantRepo.ExpectedCalls = nil

			tt.setupMocks()

			response, err := authService.Register(tt.request)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotEmpty(t, response.Token)
				assert.NotNil(t, response.User)
				assert.Equal(t, tt.request.Email, response.User.Email)
				assert.Equal(t, tt.request.TenantID, response.User.TenantID)
				assert.Empty(t, response.User.Password) // Password should be cleared
			}

			mockUserRepo.AssertExpectations(t)
			mockTenantRepo.AssertExpectations(t)
		})
	}
}

func TestAuthService_Login(t *testing.T) {
	// Set up test environment
	os.Setenv("JWT_SECRET", "test-secret")
	defer os.Unsetenv("JWT_SECRET")

	mockUserRepo := new(MockUserRepository)
	mockTenantRepo := new(MockTenantRepository)
	authService := NewAuthService(mockUserRepo, mockTenantRepo)

	// Create a test user with hashed password
	hashedPassword, _ := authService.hashPassword("Password123")
	testUser := &models.User{
		ID:       "user-123",
		TenantID: "tenant-123",
		Email:    "<EMAIL>",
		Password: hashedPassword,
		Role:     models.RoleAdministrator,
	}

	tests := []struct {
		name        string
		request     *LoginRequest
		setupMocks  func()
		expectError bool
		errorMsg    string
	}{
		{
			name: "successful login",
			request: &LoginRequest{
				TenantID: "tenant-123",
				Email:    "<EMAIL>",
				Password: "Password123",
			},
			setupMocks: func() {
				mockUserRepo.On("GetUserByEmail", "tenant-123", "<EMAIL>").Return(testUser, nil)
			},
			expectError: false,
		},
		{
			name: "invalid credentials - wrong password",
			request: &LoginRequest{
				TenantID: "tenant-123",
				Email:    "<EMAIL>",
				Password: "wrongpassword",
			},
			setupMocks: func() {
				mockUserRepo.On("GetUserByEmail", "tenant-123", "<EMAIL>").Return(testUser, nil)
			},
			expectError: true,
			errorMsg:    "invalid credentials",
		},
		{
			name: "user not found",
			request: &LoginRequest{
				TenantID: "tenant-123",
				Email:    "<EMAIL>",
				Password: "Password123",
			},
			setupMocks: func() {
				mockUserRepo.On("GetUserByEmail", "tenant-123", "<EMAIL>").Return((*models.User)(nil), assert.AnError)
			},
			expectError: true,
			errorMsg:    "invalid credentials",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mocks
			mockUserRepo.ExpectedCalls = nil
			mockTenantRepo.ExpectedCalls = nil

			tt.setupMocks()

			response, err := authService.Login(tt.request)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotEmpty(t, response.Token)
				assert.NotNil(t, response.User)
				assert.Equal(t, tt.request.Email, response.User.Email)
				assert.Equal(t, tt.request.TenantID, response.User.TenantID)
				assert.Empty(t, response.User.Password) // Password should be cleared
			}

			mockUserRepo.AssertExpectations(t)
			mockTenantRepo.AssertExpectations(t)
		})
	}
}

func TestAuthService_ValidateToken(t *testing.T) {
	// Set up test environment
	os.Setenv("JWT_SECRET", "test-secret")
	defer os.Unsetenv("JWT_SECRET")

	mockUserRepo := new(MockUserRepository)
	mockTenantRepo := new(MockTenantRepository)
	authService := NewAuthService(mockUserRepo, mockTenantRepo)

	// Create a test user and generate a token
	testUser := &models.User{
		ID:       "user-123",
		TenantID: "tenant-123",
		Email:    "<EMAIL>",
		Role:     models.RoleAdministrator,
	}

	token, err := authService.generateJWT(testUser)
	assert.NoError(t, err)

	t.Run("valid token", func(t *testing.T) {
		claims, err := authService.ValidateToken(token)
		assert.NoError(t, err)
		assert.NotNil(t, claims)
		assert.Equal(t, testUser.ID, claims.UserID)
		assert.Equal(t, testUser.TenantID, claims.TenantID)
		assert.Equal(t, testUser.Role, claims.Role)
		assert.Equal(t, testUser.Email, claims.Email)
	})

	t.Run("invalid token", func(t *testing.T) {
		claims, err := authService.ValidateToken("invalid-token")
		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("empty token", func(t *testing.T) {
		claims, err := authService.ValidateToken("")
		assert.Error(t, err)
		assert.Nil(t, claims)
	})
}
