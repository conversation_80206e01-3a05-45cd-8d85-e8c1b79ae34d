(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72],{910:(e,t,r)=>{Promise.resolve().then(r.bind(r,5604))},2942:(e,t,r)=>{"use strict";var a=r(2418);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},5604:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var a=r(4568),s=r(7620),n=r(2942),i=r(7261),o=r.n(i),l=r(8500);function d(){let e=(0,n.useRouter)(),{login:t,isLoading:r,error:i,clearError:d,isAuthenticated:c}=(0,l.nc)(),[u,m]=(0,s.useState)({tenant_id:"",email:"",password:""}),[h,g]=(0,s.useState)({});(0,s.useEffect)(()=>{c&&e.push("/dashboard")},[c,e]),(0,s.useEffect)(()=>{d()},[d]);let p=async r=>{if(r.preventDefault(),(()=>{let e={};return u.tenant_id.trim()||(e.tenant_id="Tenant ID is required"),u.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u.email)||(e.email="Please enter a valid email address"):e.email="Email is required",u.password||(e.password="Password is required"),g(e),0===Object.keys(e).length})())try{await t(u),e.push("/dashboard")}catch(e){console.error("Login failed:",e)}},x=e=>{let{name:t,value:r}=e.target;m(e=>({...e,[t]:r})),h[t]&&g(e=>({...e,[t]:void 0}))};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to OmniCore"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,a.jsx)(o(),{href:"/register",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"create a new account"})]})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:p,children:[i&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsx)("div",{className:"text-sm text-red-700",children:i})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tenant_id",className:"block text-sm font-medium text-gray-700",children:"Tenant ID"}),(0,a.jsx)("input",{id:"tenant_id",name:"tenant_id",type:"text",required:!0,value:u.tenant_id,onChange:x,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(h.tenant_id?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Enter your tenant ID"}),h.tenant_id&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.tenant_id})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:u.email,onChange:x,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(h.email?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Enter your email"}),h.email&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:u.password,onChange:x,className:"mt-1 appearance-none relative block w-full px-3 py-2 border ".concat(h.password?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"),placeholder:"Enter your password"}),h.password&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h.password})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:r,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:r?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})})]})]})})}},8500:(e,t,r)=>{"use strict";r.d(t,{As:()=>d,BG:()=>c,nc:()=>l});var a=r(1326),s=r(5313),n=r(7932);let i=r(4338).env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api/v1",o={async post(e,t,r){let a={"Content-Type":"application/json"};r&&(a.Authorization="Bearer ".concat(r));let s=await fetch("".concat(i).concat(e),{method:"POST",headers:a,body:JSON.stringify(t)}),n=await s.json();if(!s.ok)throw Error(n.message||n.error||"An error occurred");return n},async get(e,t){let r={};t&&(r.Authorization="Bearer ".concat(t));let a=await fetch("".concat(i).concat(e),{method:"GET",headers:r}),s=await a.json();if(!a.ok)throw Error(s.message||s.error||"An error occurred");return s}},l=(0,a.v)()((0,s.Zr)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{try{e({isLoading:!0,error:null});let{token:r,user:a}=(await o.post("/auth/login",t)).data;n.A.set("auth_token",r,{expires:1,secure:!0,sameSite:"strict"}),e({user:a,token:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Login failed"}),t}},register:async t=>{try{e({isLoading:!0,error:null});let{token:r,user:a}=(await o.post("/auth/register",t)).data;n.A.set("auth_token",r,{expires:1,secure:!0,sameSite:"strict"}),e({user:a,token:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Registration failed"}),t}},logout:()=>{n.A.remove("auth_token"),e({user:null,token:null,isAuthenticated:!1,error:null})},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},refreshUser:async()=>{try{let{token:r}=t();if(!r)return;let a=await o.get("/auth/me",r);e({user:a.data,isAuthenticated:!0})}catch(e){t().logout()}}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated}),onRehydrateStorage:()=>e=>{let t=n.A.get("auth_token");t&&e?(e.token=t,e.refreshUser()):e&&e.logout()}})),d=()=>{let{user:e,isAuthenticated:t,isLoading:r}=l();return{user:e,isAuthenticated:t,isLoading:r}},c=()=>{let{login:e,register:t,logout:r,clearError:a}=l();return{login:e,register:t,logout:r,clearError:a}}}},e=>{e.O(0,[767,261,587,902,358],()=>e(e.s=910)),_N_E=e.O()}]);