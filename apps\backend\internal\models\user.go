package models

import (
	"time"
	"gorm.io/gorm"
)

// User represents a user in the system with multi-tenant support
type User struct {
	ID        string    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	TenantID  string    `json:"tenant_id" gorm:"type:uuid;not null;index"`
	Email     string    `json:"email" gorm:"type:varchar(255);not null;uniqueIndex:idx_tenant_email"`
	Password  string    `json:"-" gorm:"type:varchar(255);not null"` // Hidden from JSON
	Role      string    `json:"role" gorm:"type:varchar(50);not null;default:'POS Staff'"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	
	// Relationship
	Tenant Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// Tenant represents a customer company for multi-tenant isolation
type Tenant struct {
	ID               string    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name             string    `json:"name" gorm:"type:varchar(255);not null"`
	SubscriptionPlan string    `json:"subscription_plan" gorm:"type:varchar(100);not null;default:'basic'"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// UserRole constants for role-based access control
const (
	RoleAdministrator = "Administrator"
	RoleAccountant    = "Accountant"
	RolePOSStaff      = "POS Staff"
)

// ValidRoles returns a slice of all valid user roles
func ValidRoles() []string {
	return []string{RoleAdministrator, RoleAccountant, RolePOSStaff}
}

// IsValidRole checks if the provided role is valid
func IsValidRole(role string) bool {
	for _, validRole := range ValidRoles() {
		if role == validRole {
			return true
		}
	}
	return false
}

// TableName returns the table name for User model
func (User) TableName() string {
	return "users"
}

// TableName returns the table name for Tenant model
func (Tenant) TableName() string {
	return "tenants"
}

// BeforeCreate hook to ensure tenant isolation
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.TenantID == "" {
		return gorm.ErrInvalidData
	}
	if !IsValidRole(u.Role) {
		u.Role = RolePOSStaff // Default role
	}
	return nil
}
