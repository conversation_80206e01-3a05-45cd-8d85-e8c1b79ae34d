'use client';

import { useAuth, useAuthActions } from '@/stores/auth-store';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function DashboardContent() {
  const { user } = useAuth();
  const { logout } = useAuthActions();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">OmniCore Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                Welcome, {user?.email}
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                {user?.role}
              </span>
              <button
                onClick={handleLogout}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Welcome to OmniCore!
              </h2>
              <p className="text-gray-600 mb-6">
                You have successfully logged in to the system.
              </p>
              
              <div className="bg-white shadow rounded-lg p-6 max-w-md mx-auto">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Your Profile</h3>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="text-sm font-medium text-gray-500">Email:</dt>
                    <dd className="text-sm text-gray-900">{user?.email}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm font-medium text-gray-500">Role:</dt>
                    <dd className="text-sm text-gray-900">{user?.role}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm font-medium text-gray-500">Tenant ID:</dt>
                    <dd className="text-sm text-gray-900">{user?.tenantId}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm font-medium text-gray-500">User ID:</dt>
                    <dd className="text-sm text-gray-900 font-mono text-xs">{user?.id}</dd>
                  </div>
                </dl>
              </div>

              {user?.role === 'Administrator' && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-800 font-medium">
                    🔑 Administrator Access
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    You have full access to all system features including user management.
                  </p>
                </div>
              )}

              {user?.role === 'Accountant' && (
                <div className="mt-6 p-4 bg-green-50 rounded-lg">
                  <p className="text-green-800 font-medium">
                    📊 Accountant Access
                  </p>
                  <p className="text-green-600 text-sm mt-1">
                    You have access to financial features and reporting.
                  </p>
                </div>
              )}

              {user?.role === 'POS Staff' && (
                <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                  <p className="text-purple-800 font-medium">
                    🛒 POS Staff Access
                  </p>
                  <p className="text-purple-600 text-sm mt-1">
                    You have access to point-of-sale features and inventory.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
