(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1586:(e,t,r)=>{Promise.resolve().then(r.bind(r,7738))},2942:(e,t,r)=>{"use strict";var a=r(2418);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},7738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(4568),o=r(7620),n=r(2942),s=r(8500);function i(){let e=(0,n.useRouter)(),{isAuthenticated:t,isLoading:r}=(0,s.As)();return((0,o.useEffect)(()=>{r||(t?e.push("/dashboard"):e.push("/login"))},[t,r,e]),r)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"})}):null}},8500:(e,t,r)=>{"use strict";r.d(t,{As:()=>c,BG:()=>l,nc:()=>u});var a=r(1326),o=r(5313),n=r(7932);let s=r(4338).env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api/v1",i={async post(e,t,r){let a={"Content-Type":"application/json"};r&&(a.Authorization="Bearer ".concat(r));let o=await fetch("".concat(s).concat(e),{method:"POST",headers:a,body:JSON.stringify(t)}),n=await o.json();if(!o.ok)throw Error(n.message||n.error||"An error occurred");return n},async get(e,t){let r={};t&&(r.Authorization="Bearer ".concat(t));let a=await fetch("".concat(s).concat(e),{method:"GET",headers:r}),o=await a.json();if(!a.ok)throw Error(o.message||o.error||"An error occurred");return o}},u=(0,a.v)()((0,o.Zr)((e,t)=>({user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null,login:async t=>{try{e({isLoading:!0,error:null});let{token:r,user:a}=(await i.post("/auth/login",t)).data;n.A.set("auth_token",r,{expires:1,secure:!0,sameSite:"strict"}),e({user:a,token:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Login failed"}),t}},register:async t=>{try{e({isLoading:!0,error:null});let{token:r,user:a}=(await i.post("/auth/register",t)).data;n.A.set("auth_token",r,{expires:1,secure:!0,sameSite:"strict"}),e({user:a,token:r,isAuthenticated:!0,isLoading:!1,error:null})}catch(t){throw e({isLoading:!1,error:t instanceof Error?t.message:"Registration failed"}),t}},logout:()=>{n.A.remove("auth_token"),e({user:null,token:null,isAuthenticated:!1,error:null})},clearError:()=>{e({error:null})},setLoading:t=>{e({isLoading:t})},refreshUser:async()=>{try{let{token:r}=t();if(!r)return;let a=await i.get("/auth/me",r);e({user:a.data,isAuthenticated:!0})}catch(e){t().logout()}}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated}),onRehydrateStorage:()=>e=>{let t=n.A.get("auth_token");t&&e?(e.token=t,e.refreshUser()):e&&e.logout()}})),c=()=>{let{user:e,isAuthenticated:t,isLoading:r}=u();return{user:e,isAuthenticated:t,isLoading:r}},l=()=>{let{login:e,register:t,logout:r,clearError:a}=u();return{login:e,register:t,logout:r,clearError:a}}}},e=>{e.O(0,[767,587,902,358],()=>e(e.s=1586)),_N_E=e.O()}]);